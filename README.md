# 📊 Streamlit Research Report Generator

A comprehensive web application that generates professional research reports using AI-powered web search and analysis. Built with Streamlit, Tavily API for web search, and OpenRouter API for AI-powered report generation.

## 🌟 Features

### Core Functionality
- **Intelligent Web Search**: Uses Tavily API for advanced web search and information gathering
- **AI-Powered Analysis**: Leverages OpenRouter API with Claude 3.5 Sonnet for comprehensive report generation
- **Professional Reports**: Generates well-structured, 2000-3000 word research reports in Markdown format
- **Secure API Management**: Password-protected API key inputs with session state management

### User Experience
- **Modern UI**: Clean, responsive interface with improved contrast and accessibility
- **Real-time Progress**: Loading indicators and progress bars during report generation
- **Input Validation**: Comprehensive validation for API keys and research topics
- **Smart Error Handling**: Robust error handling with specific guidance for different error types
- **Graceful Degradation**: Automatic retry with reduced token limits for credit-constrained accounts

### Report Features
- **Structured Format**: Professional report structure with executive summary, analysis, and conclusions
- **Markdown Export**: Download reports as properly formatted Markdown (.md) files
- **Citation Support**: Includes references and citations from web sources
- **Word Count Tracking**: Displays report metadata including word count and generation time

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Tavily API key ([Get one here](https://tavily.com))
- OpenRouter API key ([Get one here](https://openrouter.ai))

### Installation

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd streamlit-research-generator
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   streamlit run app.py
   ```

4. **Open your browser**
   - Navigate to `http://localhost:8501`
   - The application will open automatically

## 📖 Usage Guide

### Step 1: Configure API Keys
1. Open the application in your browser
2. In the sidebar, enter your API keys:
   - **Tavily API Key**: For web search functionality
   - **OpenRouter API Key**: For AI report generation
3. The application will validate your API key formats

### Step 2: Enter Research Topic
1. In the main area, enter your research topic
2. Be specific for better results (e.g., "Impact of AI on healthcare diagnostics")

### Step 3: Generate Report
1. Click "🚀 Generate Research Report"
2. Wait for the process to complete (typically 30-60 seconds)
3. Monitor the progress bar and status updates

### Step 4: Download and Review
1. Review the generated report in the preview section
2. Check the report metadata (word count, generation time)
3. Click "📥 Download Report as Markdown" to save the file

## 🔧 Technical Details

### Architecture
- **Frontend**: Streamlit with custom CSS styling
- **Web Search**: Tavily API with advanced search capabilities
- **AI Generation**: OpenRouter API using Claude 3.5 Sonnet model
- **State Management**: Streamlit session state for persistent data

### API Integration
- **Tavily Configuration**: Advanced search depth with raw content inclusion
- **OpenRouter Configuration**: Uses OpenAI client library with proper headers
- **Error Handling**: Comprehensive exception handling for API failures

### Security Features
- Password-protected API key inputs
- Session-based key storage (not in URLs)
- Input validation and sanitization
- Secure file naming conventions

## 📁 Project Structure

```
streamlit-research-generator/
├── app.py                 # Main Streamlit application
├── test_app.py           # Test suite for core functionality
├── requirements.txt      # Python dependencies
├── README.md            # This documentation
└── reports/             # Generated reports (created automatically)
```

## 🧪 Testing

Run the test suite to validate core functionality:

```bash
python test_app.py
```

The test suite covers:
- API key validation
- Filename sanitization
- Download filename generation
- Error handling scenarios

## 🔑 API Key Requirements

### Tavily API
- **Format**: `tvly-xxxxxxxxxxxxxxxx`
- **Purpose**: Web search and information gathering
- **Sign up**: [tavily.com](https://tavily.com)

### OpenRouter API
- **Format**: `sk-xxxxxxxxxxxxxxxx`
- **Purpose**: AI-powered report generation
- **Sign up**: [openrouter.ai](https://openrouter.ai)
- **Model Used**: Claude 3.5 Sonnet (anthropic/claude-3.5-sonnet)

## 📊 Report Structure

Generated reports include:
- **Executive Summary**: Key findings overview
- **Introduction**: Topic background and context
- **Key Findings**: Detailed analysis with subsections
- **Analysis and Insights**: Deep dive into the research
- **Current Trends**: Latest developments and trends
- **Challenges and Opportunities**: SWOT-style analysis
- **Future Outlook**: Predictions and forecasts
- **Conclusion**: Summary and final thoughts
- **References**: Citations from web sources

## 🛠️ Customization

### Modifying the Report Structure
Edit the prompt in the `generate_report_with_openrouter()` function to customize:
- Report sections and structure
- Writing style and tone
- Length and depth requirements
- Citation format

### Styling Changes
Modify the CSS in the `st.markdown()` section to customize:
- Colors and themes
- Layout and spacing
- Button styles
- Typography

### API Configuration
Adjust API parameters in the respective functions:
- Tavily search depth and result count
- OpenRouter model selection and parameters
- Token limits and temperature settings

## 🚨 Troubleshooting

### Common Issues

**"Invalid API key format"**
- Ensure your API keys match the expected format
- Check for extra spaces or characters
- Verify keys are active and have sufficient credits

**"Insufficient API Credits" (402 Error)**
- Add credits to your OpenRouter account at [openrouter.ai](https://openrouter.ai)
- The app automatically retries with fewer tokens (2800 → 2200 → 1800 → 1400)
- Try shorter, more specific research topics to reduce token usage
- Free tier provides $5 in credits, typically good for 5-10 reports

**"Web search failed"**
- Check your Tavily API key and quota
- Ensure internet connectivity
- Try a different research topic
- Tavily free tier: 1,000 searches/month

**"Report generation failed"**
- Verify your OpenRouter API key
- Check if you have sufficient credits
- Ensure the topic isn't too broad or vague
- Wait a moment and try again (rate limits may apply)

### Performance Tips
- Use specific, focused research topics for better results
- Ensure stable internet connection during generation
- Allow 30-60 seconds for complete report generation

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## 📞 Support

For support or questions:
1. Check the troubleshooting section above
2. Review the API provider documentation
3. Submit an issue on the project repository
