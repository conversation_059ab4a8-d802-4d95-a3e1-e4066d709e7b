"""
Test script for the Streamlit Research Report Generator

This script tests the core functionality of the application including:
- API key validation
- Filename sanitization
- Session state initialization
- Error handling
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import (
    validate_api_key,
    sanitize_filename,
    create_download_filename,
    initialize_session_state
)
import datetime

def test_api_key_validation():
    """Test API key validation functionality"""
    print("Testing API key validation...")
    
    # Test valid Tavily API key
    assert validate_api_key("tvly-1234567890abcdef", "tavily") == True
    print("✅ Valid Tavily API key test passed")
    
    # Test invalid Tavily API key
    assert validate_api_key("invalid-key", "tavily") == False
    print("✅ Invalid Tavily API key test passed")
    
    # Test valid OpenRouter API key
    assert validate_api_key("sk-1234567890abcdef", "openrouter") == True
    print("✅ Valid OpenRouter API key test passed")
    
    # Test invalid OpenRouter API key
    assert validate_api_key("invalid-key", "openrouter") == False
    print("✅ Invalid OpenRouter API key test passed")
    
    # Test empty API key
    assert validate_api_key("", "tavily") == False
    assert validate_api_key("", "openrouter") == False
    print("✅ Empty API key test passed")
    
    # Test short API key
    assert validate_api_key("short", "tavily") == False
    assert validate_api_key("short", "openrouter") == False
    print("✅ Short API key test passed")

def test_filename_sanitization():
    """Test filename sanitization functionality"""
    print("\nTesting filename sanitization...")
    
    # Test normal topic
    result = sanitize_filename("Artificial Intelligence in Healthcare")
    expected = "artificial_intelligence_in_healthcare"
    assert result == expected
    print(f"✅ Normal topic test passed: '{result}'")
    
    # Test topic with special characters
    result = sanitize_filename("AI & Machine Learning: The Future?")
    expected = "ai_machine_learning_the_future"
    assert result == expected
    print(f"✅ Special characters test passed: '{result}'")
    
    # Test very long topic
    long_topic = "This is a very long research topic that should be truncated to fit within reasonable filename limits"
    result = sanitize_filename(long_topic)
    assert len(result) <= 50
    print(f"✅ Long topic test passed: '{result}' (length: {len(result)})")
    
    # Test topic with numbers and hyphens
    result = sanitize_filename("COVID-19 Impact on Economy 2020-2024")
    expected = "covid_19_impact_on_economy_2020_2024"
    assert result == expected
    print(f"✅ Numbers and hyphens test passed: '{result}'")

def test_download_filename_creation():
    """Test download filename creation"""
    print("\nTesting download filename creation...")
    
    topic = "Artificial Intelligence"
    filename = create_download_filename(topic)
    
    # Check if filename has correct format
    assert filename.startswith("research_report_")
    assert filename.endswith(".md")
    assert "artificial_intelligence" in filename
    
    # Check if timestamp is included
    current_date = datetime.datetime.now().strftime("%Y%m%d")
    assert current_date in filename
    
    print(f"✅ Download filename test passed: '{filename}'")

def test_error_handling():
    """Test error handling scenarios"""
    print("\nTesting error handling...")
    
    # Test with None values
    try:
        validate_api_key(None, "tavily")
        print("✅ None API key handled gracefully")
    except Exception as e:
        print(f"❌ None API key test failed: {e}")
    
    # Test with invalid key types
    try:
        result = validate_api_key("sk-test", "invalid_type")
        print(f"✅ Invalid key type handled gracefully: {result}")
    except Exception as e:
        print(f"❌ Invalid key type test failed: {e}")

def run_all_tests():
    """Run all tests"""
    print("🧪 Starting Streamlit Research Report Generator Tests\n")
    print("=" * 60)
    
    try:
        test_api_key_validation()
        test_filename_sanitization()
        test_download_filename_creation()
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("🎉 All tests passed successfully!")
        print("✅ The application core functionality is working correctly.")
        
    except AssertionError as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_all_tests()
