"""
Streamlit Research Report Generator

A web application that generates comprehensive research reports using Tavily for web search
and OpenRouter API with OpenAI client for AI-powered report generation.

Features:
- Secure API key input with session state management
- Research topic input with validation
- Real-time report generation with loading indicators
- Markdown report display and download functionality
- Comprehensive error handling and user feedback
"""

import streamlit as st
import openai
from tavily import TavilyClient
import datetime
import re
import time
from typing import Optional, Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Research Report Generator",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    .info-box {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin: 1rem 0;
    }
    .success-box {
        background-color: #d4edda;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #28a745;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #dc3545;
        margin: 1rem 0;
    }
    .stButton > button {
        width: 100%;
        background-color: #1f77b4;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 0.25rem;
        font-weight: bold;
    }
    .stButton > button:hover {
        background-color: #1565c0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'tavily_api_key' not in st.session_state:
        st.session_state.tavily_api_key = ""
    if 'openrouter_api_key' not in st.session_state:
        st.session_state.openrouter_api_key = ""
    if 'research_topic' not in st.session_state:
        st.session_state.research_topic = ""
    if 'generated_report' not in st.session_state:
        st.session_state.generated_report = ""
    if 'report_generated' not in st.session_state:
        st.session_state.report_generated = False
    if 'is_generating' not in st.session_state:
        st.session_state.is_generating = False
    if 'last_generation_time' not in st.session_state:
        st.session_state.last_generation_time = None

def validate_api_key(api_key: str, key_type: str) -> bool:
    """Validate API key format"""
    if not api_key or len(api_key.strip()) < 10:
        return False
    
    # Basic validation patterns
    if key_type == "tavily" and not api_key.startswith("tvly-"):
        return False
    elif key_type == "openrouter" and not api_key.startswith("sk-"):
        return False
    
    return True

def sanitize_filename(topic: str) -> str:
    """Sanitize research topic for use in filename"""
    # Remove special characters and replace spaces with underscores
    sanitized = re.sub(r'[^\w\s-]', '', topic)
    sanitized = re.sub(r'[-\s]+', '_', sanitized)
    return sanitized.lower()[:50]  # Limit length

def create_download_filename(topic: str) -> str:
    """Create a properly formatted filename for the report download"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    sanitized_topic = sanitize_filename(topic)
    return f"research_report_{sanitized_topic}_{timestamp}.md"

def search_with_tavily(client: TavilyClient, query: str, max_results: int = 10) -> Dict[str, Any]:
    """Perform web search using Tavily API"""
    try:
        logger.info(f"Searching for: {query}")
        search_result = client.search(
            query=query,
            search_depth="advanced",
            max_results=max_results,
            include_answer=True,
            include_raw_content=True
        )
        return search_result
    except Exception as e:
        logger.error(f"Tavily search error: {str(e)}")
        raise Exception(f"Web search failed: {str(e)}")

def generate_report_with_openrouter(
    openrouter_api_key: str,
    topic: str,
    search_results: Dict[str, Any]
) -> str:
    """Generate research report using OpenRouter API with OpenAI client"""
    try:
        # Initialize OpenAI client with OpenRouter configuration
        client = openai.OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=openrouter_api_key,
            default_headers={
                "HTTP-Referer": "https://streamlit-research-generator.app",
                "X-Title": "Research Report Generator"
            }
        )

        # Prepare search context
        search_context = ""
        if search_results.get("results"):
            search_context = "\n\n".join([
                f"**Source: {result.get('title', 'Unknown')}**\n"
                f"URL: {result.get('url', 'N/A')}\n"
                f"Content: {result.get('content', 'No content available')[:1000]}..."
                for result in search_results["results"][:8]
            ])

        # Include Tavily's answer if available
        tavily_answer = search_results.get("answer", "")

        # Create comprehensive prompt
        prompt = f"""
You are a professional research analyst tasked with creating a comprehensive research report on the topic: "{topic}"

Based on the following web search results and information, create a detailed, well-structured research report in Markdown format.

TAVILY AI SUMMARY:
{tavily_answer}

SEARCH RESULTS:
{search_context}

REQUIREMENTS:
1. Create a comprehensive report with the following structure:
   - Executive Summary
   - Introduction
   - Key Findings (with multiple subsections)
   - Analysis and Insights
   - Current Trends and Developments
   - Challenges and Opportunities
   - Future Outlook
   - Conclusion
   - References

2. Use proper Markdown formatting with:
   - Clear headers (# ## ###)
   - Bullet points and numbered lists
   - Bold and italic text for emphasis
   - Tables where appropriate
   - Proper citation format

3. Ensure the report is:
   - Factual and well-researched
   - Professional in tone
   - Comprehensive (aim for 2000-3000 words)
   - Well-organized and easy to read
   - Includes specific data, statistics, and examples from the sources

4. Include proper citations and references to the sources provided

Generate the complete research report now:
"""

        logger.info("Generating report with OpenRouter API")

        # Make API call to generate report
        response = client.chat.completions.create(
            model="anthropic/claude-3.5-sonnet",  # Using Claude 3.5 Sonnet for high-quality reports
            messages=[
                {
                    "role": "system",
                    "content": "You are a professional research analyst who creates comprehensive, well-structured research reports in Markdown format. Your reports are thorough, factual, and professionally formatted."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            max_tokens=4000,
            temperature=0.3
        )

        report_content = response.choices[0].message.content
        logger.info("Report generated successfully")

        return report_content

    except Exception as e:
        logger.error(f"OpenRouter API error: {str(e)}")
        raise Exception(f"Report generation failed: {str(e)}")

def generate_research_report(tavily_api_key: str, openrouter_api_key: str, topic: str) -> str:
    """Main function to generate a complete research report"""
    try:
        # Initialize Tavily client
        tavily_client = TavilyClient(api_key=tavily_api_key)

        # Perform web search
        st.info("🔍 Searching the web for relevant information...")
        search_results = search_with_tavily(tavily_client, topic)

        # Generate report
        st.info("🤖 Generating comprehensive research report...")
        report = generate_report_with_openrouter(openrouter_api_key, topic, search_results)

        return report

    except Exception as e:
        logger.error(f"Report generation error: {str(e)}")
        raise e

def main():
    """Main application function"""
    # Initialize session state
    initialize_session_state()
    
    # Main header
    st.markdown('<div class="main-header">📊 Research Report Generator</div>', unsafe_allow_html=True)
    
    # Description
    st.markdown("""
    <div class="info-box">
        <strong>Generate comprehensive research reports on any topic!</strong><br>
        This application uses Tavily for web search and OpenRouter API for AI-powered analysis 
        to create detailed, well-structured research reports in Markdown format.
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar for API configuration
    with st.sidebar:
        st.markdown('<div class="section-header">🔑 API Configuration</div>', unsafe_allow_html=True)
        
        # Tavily API Key
        tavily_key = st.text_input(
            "Tavily API Key",
            type="password",
            value=st.session_state.tavily_api_key,
            help="Enter your Tavily API key for web search functionality",
            placeholder="tvly-xxxxxxxxxxxxxxxx"
        )
        
        # OpenRouter API Key
        openrouter_key = st.text_input(
            "OpenRouter API Key",
            type="password",
            value=st.session_state.openrouter_api_key,
            help="Enter your OpenRouter API key for AI report generation",
            placeholder="sk-xxxxxxxxxxxxxxxx"
        )
        
        # Update session state
        st.session_state.tavily_api_key = tavily_key
        st.session_state.openrouter_api_key = openrouter_key
        
        # API Key validation status
        tavily_valid = validate_api_key(tavily_key, "tavily")
        openrouter_valid = validate_api_key(openrouter_key, "openrouter")
        
        if tavily_key:
            if tavily_valid:
                st.success("✅ Tavily API key format valid")
            else:
                st.error("❌ Invalid Tavily API key format")
        
        if openrouter_key:
            if openrouter_valid:
                st.success("✅ OpenRouter API key format valid")
            else:
                st.error("❌ Invalid OpenRouter API key format")
        
        # Clear sensitive data button
        if st.button("🗑️ Clear API Keys"):
            st.session_state.tavily_api_key = ""
            st.session_state.openrouter_api_key = ""
            st.rerun()
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.markdown('<div class="section-header">📝 Research Configuration</div>', unsafe_allow_html=True)
        
        # Research topic input
        research_topic = st.text_input(
            "Research Topic",
            value=st.session_state.research_topic,
            help="Enter the topic you want to research",
            placeholder="e.g., 'Impact of artificial intelligence on healthcare'"
        )
        st.session_state.research_topic = research_topic
        
        # Generate report button
        can_generate = (
            research_topic.strip() and 
            tavily_valid and 
            openrouter_valid and 
            not st.session_state.is_generating
        )
        
        if st.button("🚀 Generate Research Report", disabled=not can_generate):
            if can_generate:
                st.session_state.is_generating = True
                st.rerun()

        # Show requirements if not met
        if not can_generate and not st.session_state.is_generating:
            missing_items = []
            if not research_topic.strip():
                missing_items.append("Research topic")
            if not tavily_valid:
                missing_items.append("Valid Tavily API key")
            if not openrouter_valid:
                missing_items.append("Valid OpenRouter API key")

            if missing_items:
                st.warning(f"⚠️ Please provide: {', '.join(missing_items)}")

    with col2:
        st.markdown('<div class="section-header">ℹ️ Instructions</div>', unsafe_allow_html=True)
        st.markdown("""
        **Steps to generate a report:**
        1. Enter your API keys in the sidebar
        2. Specify your research topic
        3. Click "Generate Research Report"
        4. Wait for the report to be generated
        5. Download the report as a Markdown file

        **API Key Requirements:**
        - **Tavily**: Sign up at [tavily.com](https://tavily.com)
        - **OpenRouter**: Get your key at [openrouter.ai](https://openrouter.ai)
        """)

    # Handle report generation
    if st.session_state.is_generating:
        st.markdown('<div class="section-header">🔄 Generating Report...</div>', unsafe_allow_html=True)

        # Show progress
        progress_bar = st.progress(0)
        status_text = st.empty()

        try:
            # Update progress
            progress_bar.progress(10)
            status_text.text("Initializing research process...")
            time.sleep(0.5)

            # Generate the report
            progress_bar.progress(30)
            status_text.text("Searching the web for information...")

            report = generate_research_report(
                st.session_state.tavily_api_key,
                st.session_state.openrouter_api_key,
                st.session_state.research_topic
            )

            progress_bar.progress(90)
            status_text.text("Finalizing report...")
            time.sleep(0.5)

            # Store the generated report
            st.session_state.generated_report = report
            st.session_state.report_generated = True
            st.session_state.last_generation_time = datetime.datetime.now()

            progress_bar.progress(100)
            status_text.text("Report generated successfully!")
            time.sleep(1)

            # Reset generating state
            st.session_state.is_generating = False
            st.success("✅ Research report generated successfully!")
            st.rerun()

        except Exception as e:
            st.session_state.is_generating = False
            st.error(f"❌ Error generating report: {str(e)}")
            progress_bar.empty()
            status_text.empty()

    # Display generated report
    if st.session_state.report_generated and st.session_state.generated_report:
        st.markdown('<div class="section-header">📊 Generated Research Report</div>', unsafe_allow_html=True)

        # Report metadata
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Topic", st.session_state.research_topic)
        with col2:
            if st.session_state.last_generation_time:
                st.metric("Generated", st.session_state.last_generation_time.strftime("%Y-%m-%d %H:%M"))
        with col3:
            word_count = len(st.session_state.generated_report.split())
            st.metric("Word Count", f"{word_count:,}")

        # Download button
        filename = create_download_filename(st.session_state.research_topic)
        st.download_button(
            label="📥 Download Report as Markdown",
            data=st.session_state.generated_report,
            file_name=filename,
            mime="text/markdown",
            help="Download the research report as a Markdown (.md) file"
        )

        # Display report content
        st.markdown("### Report Preview")
        st.markdown(st.session_state.generated_report)

        # Option to generate a new report
        if st.button("🔄 Generate New Report"):
            st.session_state.generated_report = ""
            st.session_state.report_generated = False
            st.session_state.last_generation_time = None
            st.rerun()

if __name__ == "__main__":
    main()
